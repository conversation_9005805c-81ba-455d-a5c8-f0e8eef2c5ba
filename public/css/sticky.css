/* 粘性元素样式 */

/* 添加跟随页面滚动的样式 */
.sticky-container {
  position: relative; /* 相对定位，作为参考点 */
}

.sticky-element {
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  /* 确保sticky元素在固定定位时不会覆盖弹出框 */
  z-index: 100;
}

/* 确保悬停图片容器始终在最上层，不被任何元素遮挡 */
.event-hover-image {
  z-index: 99999 !important;
}

/* 当元素固定时，保持原来的空间 */
.sticky-placeholder {
  display: none; /* 默认隐藏 */
}

/* 显示占位符，保持布局 */
.show-placeholder {
  display: block;
}

/* 右侧边栏粘性容器样式 */
.right-sidebar-sticky-container {
  max-height: calc(100vh - 80px);
  overflow-y: auto;
  /* 确保不会覆盖弹出框，但要高于控制台 */
  z-index: 999 !important;
}

/* 右侧边栏粘性容器内的元素样式 */
.right-sidebar-sticky-container .float1,
.right-sidebar-sticky-container .float2,
.right-sidebar-sticky-container .news-ad {
  margin-bottom: 20px;
  width: 260px !important;
}

.right-sidebar-sticky-container .float1:last-child,
.right-sidebar-sticky-container .float2:last-child,
.right-sidebar-sticky-container .news-ad:last-child {
  margin-bottom: 0;
}

/* 确保粘性容器中的图片保持原有尺寸 */
.right-sidebar-sticky-container img {
  width: 260px !important;
  height: 140px !important;
  object-fit: cover;
}

/* 确保粘性容器中的元素布局正确 */
.right-sidebar-sticky-container .float1,
.right-sidebar-sticky-container .float2 {
  display: block;
  width: 260px !important;
}

.right-sidebar-sticky-container .news-ad {
  display: block;
  width: 260px !important;
  flex-shrink: 0;
}

/* 占位符样式 */
.sticky-placeholder-sidebar,
.sticky-placeholder-news-ad {
  visibility: hidden;
}

/* article页面活动日历的特定样式 */
#article-events-container {
  position: relative;
  width: 260px; /* 保持与main-right相同的宽度 */
  margin-top: 15px; /* 保持原有的margin-top */
}

#article-events-sticky {
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  z-index: 100;
  width: 260px; /* 确保宽度一致 */
}

#article-events-placeholder {
  display: none;
  width: 260px; /* 占位符也要保持宽度 */
}

#article-events-placeholder.show-placeholder {
  display: block;
}

/* category页面活动日历的特定样式 */
#category-events-container {
  position: relative;
  width: 260px; /* 保持原始宽度 */
}

#category-events-sticky {
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  z-index: 100;
  width: 260px; /* 确保宽度一致 */
}

#category-events-placeholder {
  display: none;
  width: 260px; /* 占位符也要保持宽度 */
}

#category-events-placeholder.show-placeholder {
  display: block;
}
